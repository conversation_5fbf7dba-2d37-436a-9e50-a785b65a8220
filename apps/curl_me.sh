
# QFlowAgent API 调试命令集合
# 使用方法：直接复制粘贴需要的命令到终端执行
# 服务地址：http://localhost:2026
# API文档：http://localhost:2026/docs

# ===== curl 命令参数说明 =====
# curl 是一个用于发送HTTP请求的命令行工具，以下是常用参数：
#
# -X GET/POST    : 指定HTTP请求方法 (GET获取数据, POST发送数据)
# -H 'header'    : 添加HTTP请求头 (如 Content-Type: application/json)
# -d 'data'      : 发送POST请求的数据内容 (JSON格式)
# -s             : 静默模式，不显示进度条和错误信息，只显示结果
# -v             : 详细模式，显示完整的请求和响应信息 (用于调试)
# -o filename    : 将响应保存到文件而不是显示在终端
# -w '%{http_code}' : 只显示HTTP状态码 (200=成功, 404=未找到, 500=服务器错误)
# | jq '.'       : 将JSON响应格式化显示 (需要安装jq工具)
# | jq -r '.field' : 提取JSON中的特定字段值
#

# ===== 健康检查 =====
curl -X GET http://localhost:2026/
curl -X GET http://localhost:2026/health
curl -X GET http://localhost:2026/api/health/
curl -X GET http://localhost:2026/api/health/db
curl -X GET http://localhost:2026/api/health/db/ocean
curl -X GET http://localhost:2026/api/health/db/themis

# ===== 任务管理 =====
# 获取任务列表
curl -X GET http://localhost:2026/api/tasks/
curl -X GET 'http://localhost:2026/api/tasks/?limit=1&offset=0'

# 创建新任务
curl -X POST http://localhost:2026/api/tasks/ -H 'Content-Type: application/json' -d '{"user_input": "分析**********推流成功率", "max_parallel_workers": 5, "recursion_limit": 30}'

# 获取特定任务详情 (替换 YOUR_THREAD_ID)
curl -X GET http://localhost:2026/api/tasks/web_c666444a-034f-4d26-b942-f3c35c637857

# ===== 流式任务执行 =====
# 流式执行任务 (替换 YOUR_THREAD_ID)
curl -X POST http://localhost:2026/api/tasks/YOUR_THREAD_ID/stream -H 'Content-Type: application/json' -H 'Accept: text/event-stream' -d '{"user_input": "分析**********推流成功率", "max_parallel_workers": 3, "recursion_limit": 50}'

# ===== 常用测试命令 =====
# 快速健康检查
curl -s http://localhost:2026/api/health/ | jq '.'

# 快速创建测试任务
curl -s -X POST http://localhost:2026/api/tasks/ -H 'Content-Type: application/json' -d '{"user_input": "分析**********推流成功率", "max_parallel_workers": 2, "recursion_limit": 30}' | jq '.'

# 获取最近5个任务
curl -s 'http://localhost:2026/api/tasks/?limit=5' | jq '.tasks[] | {thread_id, status, user_input}'

# ===== 调试命令 =====
# 显示详细请求信息
curl -v -X GET http://localhost:2026/api/health/

# 只显示HTTP状态码
curl -s -o /dev/null -w '%{http_code}' http://localhost:2026/api/health/

# 保存响应到文件
curl -X GET http://localhost:2026/api/tasks/ -o tasks_response.json

# ===== 实际使用示例 =====
# 示例1: 创建任务并获取ID
curl -s -X POST http://localhost:2026/api/tasks/ -H 'Content-Type: application/json' -d '{"user_input": "分析**********推流成功率", "max_parallel_workers": 3, "recursion_limit": 50}' | jq -r '.thread_id'

# 示例2: 检查数据库连接
curl -s http://localhost:2026/api/health/db | jq '.databases | to_entries[] | {database: .key, status: .value.status, message: .value.message}'

# 示例3: 获取任务状态
curl -s http://localhost:2026/api/tasks/web_12345678-1234-1234-1234-123456789012 | jq '{thread_id, status, created_at, user_input}'

# ===== 提示 =====
# 1. 替换 YOUR_THREAD_ID 为实际的任务ID
# 2. 根据需要调整请求参数
# 3. 使用 jq 工具格式化JSON输出
# 4. 查看 http://localhost:2026/docs 获取完整API文档

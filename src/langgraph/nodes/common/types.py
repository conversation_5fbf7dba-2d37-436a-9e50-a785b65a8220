import json
from typing import Any, List, Optional

from langchain_core.messages import AIMessage
from pydantic import BaseModel, ConfigDict, Field

from src.langgraph.zego_tools.ocean_executor import SqlExecutorResult
from src.langgraph.zego_tools.sql_generator import DataQueryParams


def z_to_json_str(evalue: Any) -> Any:
    def deep_convert_to_dict(obj: Any) -> Any:
        """递归将对象转换为字典，避免双重序列化"""
        if hasattr(obj, "model_dump"):
            # 标记exclude的字段会被 model_dump 跳过
            return {k: deep_convert_to_dict(v) for k, v in obj.model_dump().items()}
        elif hasattr(obj, "to_dict"):
            return {k: deep_convert_to_dict(v) for k, v in obj.to_dict().items()}
        elif isinstance(obj, dict):
            return {k: deep_convert_to_dict(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [deep_convert_to_dict(v) for v in obj]
        else:
            return obj

    return json.dumps(deep_convert_to_dict(evalue))


class WorkerParams(BaseModel):
    """
    工作参数类：封装查询参数和查询结果/错误信息
    用于在分析节点中传递数据和错误状态
    """

    query_params: DataQueryParams = Field(..., description="数据查询参数")
    sql: Optional[str] = Field(default=None, description="生成的SQL语句")
    sql_result: Optional[SqlExecutorResult] = Field(default=None, exclude=True, description="SQL 执行完整结果")

    model_config = ConfigDict(arbitrary_types_allowed=True)

    def has_data(self) -> bool:
        """检查是否有有效数据"""
        if self.sql_result is None:
            return False
        try:
            df = self.sql_result.data
            return bool(self.sql_result.is_successful and df is not None and not df.empty)
        except Exception:
            return False

    def has_error(self) -> bool:
        """检查是否有错误"""
        # 占位 WorkerParams（无结果）不视为错误
        if self.sql_result is None:
            return False
        return (not self.sql_result.is_successful) or (self.sql_result.error_message is not None)

    def get_data_prompt(self) -> str:
        """获取数据提示词格式"""
        if self.has_data():
            df = self.sql_result.data  # type: ignore[union-attr]
            data_prompt = f"<数据说明>\n{self.query_params.to_prompt()}\n</数据说明>\n"
            # 显式传入列名，避免表头缺失
            data_prompt += f"<数据表>\n{df.to_markdown(index=False, headers=list(df.columns))}\n</数据表>\n"

            data_len = len(df)
            if data_len > 3000:
                data_prompt += "\n<warning>\n数据量被限制limit3000\n</warning>\n"

            if self.sql_result and self.sql_result.additional_info and "错误码说明" in self.sql_result.additional_info:
                data_prompt += f"\n<错误码说明>\n{self.sql_result.additional_info}\n</错误码说明>\n"

            return data_prompt
        else:
            err = None
            if self.sql_result is not None:
                err = self.sql_result.error_message
            return f"**{self.query_params.query_title}**: 查询失败 - {err}"

    def get_error_context(self) -> str:
        """获取错误上下文信息，用于大模型调整参数"""
        if not self.has_error():
            return ""

        error_context = "查询失败详情：\n"
        error_context += f"- 查询标题：{self.query_params.query_title}\n"
        error_context += f"- 指标名称：{self.query_params.metric_name}\n"

        if self.sql:
            error_context += f"- 生成的SQL：\n```sql\n{self.sql}\n```\n"

        if self.sql_result and self.sql_result.error_source:
            error_context += f"- 错误来源：{self.sql_result.error_source}\n"

        if self.sql_result and self.sql_result.error_code:
            error_context += f"- 错误代码：{self.sql_result.error_code}\n"

        if self.sql_result and self.sql_result.error_message:
            error_context += f"- 错误信息：{self.sql_result.error_message}\n"

        if self.query_params.drilldown_dimension:
            error_context += f"- 下钻维度：{self.query_params.drilldown_dimension}\n"

        if self.query_params.where:
            error_context += f"- WHERE条件：{self.query_params.where}\n"

        return error_context


class RetryParams(BaseModel):
    """
    重试参数类：用于大模型调整查询参数的响应
    """

    thinking: str = Field(..., description="对错误的分析和调整思路")
    adjusted_query_params: Optional[DataQueryParams] = Field(
        default=None, description="调整后的查询参数，如果决定重试的话"
    )
    give_up: bool = Field(..., description="是否放弃这个查询任务")
    give_up_reason: Optional[str] = Field(default=None, description="放弃的原因说明")


class WorkerResult(BaseModel):
    objective_evidence: str = Field(..., description="客观数据证据（纯事实描述）")
    subjective_thinking: str = Field(..., description="逻辑清晰的分析过程")
    objective_conclusion: str = Field(..., description="简洁明了的分析结论")

    def to_message(self, extra_kwargs: dict = {}) -> AIMessage:
        """生成通用分析消息（基于 Worker 的输出）。

        - 名称优先使用传入的 worker_params.query_params.query_title
        - 若无则退化为通用标题
        """
        display_name = "分析结果"
        try:
            wp = extra_kwargs.get("worker_params") if isinstance(extra_kwargs, dict) else None
            qp = getattr(wp or object(), "query_params", None)
            qt = getattr(qp or object(), "query_title", None)
            mn = getattr(qp or object(), "metric_name", None)
            display_name = qt or mn or display_name
        except Exception:
            pass

        ai_message = AIMessage(name=str(display_name), content="")
        ai_message.additional_kwargs = {
            "object_type": self.__class__.__name__,
            "object": self,
            **extra_kwargs,
        }
        content_lines = [
            "🧾 客观数据证据（纯事实描述）",
            str(self.objective_evidence or ""),
            "",
            "🧠 分析过程",
            str(self.subjective_thinking or ""),
            "",
            "✅ 分析结论",
            str(self.objective_conclusion or ""),
        ]
        ai_message.content = "\n".join(content_lines)
        return ai_message


class ReporterResult(BaseModel):
    """
    Reporter 节点最终结构化汇报。

    要求：结论先行，逻辑清晰，有证据、有思考、言简意赅。
    """

    thinking: str = Field(..., description="综合分析的思考与取舍理由（简洁版）")
    executive_summary: str = Field(..., description="执行摘要/结论先行")
    key_findings: list[str] = Field(..., description="关键发现（要点列举，含维度/时间/地区/客户等信息）")
    supporting_evidence: list[str] = Field(..., description="关键证据与数据要点摘录（引用上游维度分析）")
    analysis_reasoning: str = Field(..., description="推理链路与因果说明（简洁版）")
    has_critical_issues: bool = Field(..., description="是否存在关键/严重问题")
    critical_issues: str = Field(..., description="关键问题详情")

    def to_message(self, extra_kwargs: dict = {}) -> AIMessage:
        ai_message = AIMessage(name="最终汇报", content="")
        ai_message.additional_kwargs = {
            "object_type": self.__class__.__name__,
            "object": self,
            **extra_kwargs,
        }

        def format_list(lst):
            if not lst:
                return ""
            return "\n".join(f"- {item}" for item in lst)

        content_lines = [
            "📌 执行摘要",
            self.executive_summary,
            "",
            f"关键问题：{'是' if self.has_critical_issues else '否'}",
            "",
            "🔎 关键发现",
            format_list(self.key_findings),
            "",
            "🧪 证据要点" if self.supporting_evidence else None,
            format_list(self.supporting_evidence) if self.supporting_evidence else None,
            "",
            "🧠 推理链路",
            self.analysis_reasoning,
            "",
            "⚠️ 关键问题详情" if (self.has_critical_issues and self.critical_issues) else None,
            self.critical_issues if (self.has_critical_issues and self.critical_issues) else None,
            "",
        ]
        ai_message.content = "\n".join([c for c in content_lines if c is not None])
        return ai_message


class DAGPlanTask(BaseModel):
    """计划中的单个任务。

    不区分 kind；计划属于整个任务。允许引用 WorkerParams 以承载数据查询/分析任务。
    状态：PENDING/READY/RUNNING/DONE/FAILED
    """

    id: int = Field(..., description="任务整数ID，从1开始连续编号且在一次规划内唯一")
    depends_on: list[int] = Field(default_factory=list, description="前置依赖任务的ID列表")
    status: str = Field(default="PENDING", description="任务状态：PENDING/READY/RUNNING/DONE/FAILED")
    worker_params: WorkerParams = Field(default=None, description="数据分析任务参数")

    model_config = ConfigDict(arbitrary_types_allowed=True)


class DAGPlan(BaseModel):
    """一次完整用户任务的计划。"""

    goal: str = Field(..., description="本次任务的总体目标/用户问题")
    tasks: list[DAGPlanTask] = Field(default_factory=list, description="任务列表")
    thinking: str = Field(default=None, description="计划思路")

    def to_message(self, extra_kwargs: dict = {}) -> AIMessage:
        from src.langgraph.tasks.planner_utils import build_plan_message

        return build_plan_message(self, extra_kwargs)

    def to_params_list(self) -> List[DataQueryParams]:
        return [t.worker_params.query_params for t in (getattr(self, "tasks", []) or []) if t.worker_params]

    # ---- Runtime helpers ----
    def get_completed_ids(self) -> set[int]:
        from src.langgraph.tasks.planner_utils import get_completed_ids

        return get_completed_ids(getattr(self, "tasks", []) or [])

    def get_ready_tasks(self) -> list[DAGPlanTask]:
        from src.langgraph.tasks.planner_utils import get_ready_tasks_by_id

        return get_ready_tasks_by_id(getattr(self, "tasks", []) or [])

    def find_task_by_id(self, task_id: int) -> DAGPlanTask | None:
        for t in getattr(self, "tasks", []) or []:
            if getattr(t, "id", None) == task_id:
                return t
        return None

    def with_tasks(self, tasks: list[DAGPlanTask]) -> "DAGPlan":
        return DAGPlan(goal=self.goal, tasks=list(tasks or []), thinking=self.thinking)


# LLM 输出用的轻量规划规范，避免在规划阶段要求提供 WorkerParams
class DAGPlanTaskSchema(BaseModel):
    """Planner 输出的单个任务规范，包含依赖与查询参数。

    说明：
    - 规划阶段以整数 id 作为任务索引与依赖引用（更易让大模型生成且避免字符串碰撞）
    - 运行时会将 id 转换为唯一键（query_params.to_key()），并把依赖映射到对应唯一键
    """

    id: int = Field(..., description="任务整数ID，从1开始连续编号且在一次规划内唯一")
    depends_on: list[int] = Field(default_factory=list, description="前置依赖的任务ID列表")
    query_params: DataQueryParams = Field(..., description="数据查询参数")


class DAGPlanSchema(BaseModel):
    """Planner 输出的 DAG 计划规范。"""

    goal: str = Field(..., description="本次任务的总体目标/用户问题")
    thinking: str = Field(default=None, description="计划思路")
    tasks: list[DAGPlanTaskSchema] = Field(default_factory=list, description="任务规范列表（带依赖）")

    def to_plan(self) -> DAGPlan:
        """将 LLM 输出的 DAGPlanSchema 转为运行时 DAGPlan（占位 WorkerParams）。

        - 直接使用整数 id 与 depends_on（整数）
        - 注入占位 WorkerParams（仅含 query_params）
        """
        tasks: List[DAGPlanTask] = []
        for task_schema in self.tasks:
            placeholder_wp = WorkerParams(query_params=task_schema.query_params)
            dep_ids: List[int] = [int(dep_id) for dep_id in (task_schema.depends_on or [])]

            tasks.append(
                DAGPlanTask(
                    id=int(task_schema.id),
                    status="PENDING",
                    worker_params=placeholder_wp,
                    depends_on=dep_ids,
                )
            )
        return DAGPlan(goal=self.goal, tasks=tasks, thinking=self.thinking)

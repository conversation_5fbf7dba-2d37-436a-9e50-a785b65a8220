"""数据查询参数定义模块"""

import hashlib
from datetime import datetime, timedelta
from typing import Literal, Optional

from pydantic import BaseModel, Field

from src.langgraph.zego_tools.sql_generator.metrics import metrics_registry, ZEGOMetric
from src.langgraph.zego_tools.sql_generator.utils import TableGranularityManager


class DataQueryParams(BaseModel):
    """数据查询参数类，定义了所有查询相关的参数和配置"""

    metric_name: Literal[
        # 默认指标
        "拉流成功率",
        "登录成功率",
        "5s登录成功率",
        "推流成功率",
        "视频卡顿率",
        "拉流丢包率",
        "推流丢包率",
        "统一接入request成功率",
        "统一接入connect成功率",
        "3s拉流请求成功率",
        "3s推流请求成功率",
        "推流rtc子事件成功率",
        "拉流rtc子事件成功率",
        "拉流l3子事件成功率",
        "拉流调度成功率",
        "推流调度成功率",
        "视频首帧耗时均值",
        "拉流请求耗时均值",
        "推流请求耗时均值",
        "拉流请求耗时1s占比",
        "推流请求耗时1s占比",
        "端到端丢包率",
        # 错误码分布指标
        "拉流错误码分布",
        "登录错误码分布",
        "5s登录错误码分布",
        "推流错误码分布",
        "统一接入request错误码分布",
        "统一接入connect错误码分布",
        "3s拉流请求错误码分布",
        "3s推流请求错误码分布",
        "推流rtc子事件错误码分布",
        "拉流rtc子事件错误码分布",
        "拉流l3子事件错误码分布",
        "拉流调度错误码分布",
        "推流调度错误码分布",
    ] = Field(default="拉流成功率", description="指标名称，包括默认指标和错误码分布指标")

    drilldown_dimension: Optional[
        Literal[
            "app_id",
            "country",
            "sdk_version",
            "isp",
            "src",
            # "platform",
            # "os_type",
            # "network_type",
        ]
    ] = Field(
        default=None,
        description="可选的下钻维度字段。如果为空则进行普通趋势分析，如果指定则进行该维度的group by下钻分析；只能填单个维度，不能填多个维度；如果已经指定了该维度的where筛选，则再下钻该维度是无意义。下钻isp时需要在where中指定国家",
    )

    where: Optional[str] = Field(
        default=None,
        description="额外的 WHERE 条件，如 \"platform='Android'\" 或 \"country='中国' AND platform='iOS'\"。用于进一步过滤数据",
    )

    time_start: str = Field(
        default=(datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d %H:%M:%S"),
        description="数据查询的开始时间，默认30天前；注意，不同时间范围数据精度不同，7天以上数据为天粒度，7天内为小时粒度，2天内为10分钟粒度，4小时内为分钟粒度；格式为YYYY-MM-DD HH:MM:SS",
    )
    time_end: str = Field(
        default=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        description="数据查询的结束时间，默认当前时间；注意，不同时间范围数据精度不同，7天以上数据为天粒度，7天内为小时粒度，2天内为10分钟粒度，4小时内为分钟粒度；格式为YYYY-MM-DD HH:MM:SS。",
    )

    appid_filter: Optional[int] = Field(
        default=None,
        description="数据查询的客户AppID过滤条件, 如123456",
    )

    country_filter: Optional[str] = Field(
        default=None,
        description="数据查询的国家过滤条件, 如'沙特阿拉伯'，注意，country使用中文而非国家代码",
    )

    query_title: str = Field(
        default="",
        description="查询标题，由大模型生成，用于解释为什么要进行这次查询，如'分析沙特阿拉伯拉流成功率下降原因'、'下钻分析运营商维度拉流成功率'",
    )

    def build_table_str(self, metric: ZEGOMetric) -> str:
        """
        构建表名
        """
        start_time = datetime.strptime(self.time_start, "%Y-%m-%d %H:%M:%S")
        end_time = datetime.strptime(self.time_end, "%Y-%m-%d %H:%M:%S")
        granularity = TableGranularityManager.auto_select_granularity(start_time, end_time)
        return metric.get_table_name(granularity)

    def build_where_condition_str(self, metric: ZEGOMetric) -> str:
        """
        构建WHERE条件
        """
        where_conditions = [f"{metric.time_field} BETWEEN '{self.time_start}' AND '{self.time_end}'"]
        if self.appid_filter:
            where_conditions.append(f"(app_id = {self.appid_filter})")
        if self.country_filter:
            where_conditions.append(f"(country = '{self.country_filter}')")
        if self.where:
            local_where = self.where.replace("error_code", metric.error_field).strip()
            # 使用局部变量，避免修改原始参数；并在包含 OR 时为表达式整体加括号，防止 AND/OR 优先级导致的大查询
            where_conditions.append(f"({local_where})")
        if metric.where_filters:
            mf = metric.where_filters.strip()
            where_conditions.append(f"({mf})")

        return " AND ".join(where_conditions)

    def to_message(self) -> str:
        return f"{self.query_title}({self.model_dump_json(exclude=['query_title'])})"

    def to_key(self) -> str:
        """
        生成基于参数内容的唯一key，用于数据存储和缓存

        使用参数序列化+SHA256哈希的方式确保：
        1. 参数相同时key相同
        2. 参数不同时key不同
        3. key长度固定，避免过长
        4. 版本迭代时保持稳定性

        Returns:
            str: 16位的唯一key，格式为hex字符串
        """
        # 使用SHA256生成hash，取前16位作为key
        hash_obj = hashlib.sha256(self.model_dump_json().encode("utf-8"))
        return hash_obj.hexdigest()[:16]

    def to_prompt(self) -> str:
        """
        将查询参数转换为大模型可理解的提示词格式
        帮助大模型理解当前数据的查询条件和背景
        """
        time_range = f"{self.time_start} 至 {self.time_end}"

        prompt_parts = []

        # 如果有查询标题，先显示标题
        if self.query_title:
            prompt_parts.extend([f"**查询标题**：{self.query_title}", ""])

        prompt_parts.extend(
            [
                "**数据查询参数说明**：",
                f"• 指标：{self.metric_name}",
                f"• 时间范围：{time_range}",
            ]
        )

        if self.drilldown_dimension:
            prompt_parts.append(f"• 下钻维度：{self.drilldown_dimension}")

        if self.where:
            prompt_parts.append(f"• 额外过滤条件：{self.where}")

        if self.appid_filter:
            prompt_parts.append(f"• 客户AppID过滤：{self.appid_filter}")

        if self.country_filter:
            prompt_parts.append(f"• 国家过滤：{self.country_filter}")

        # 添加查询参数的含义说明
        analysis_desc = "维度下钻分析" if self.drilldown_dimension else "指标趋势分析"

        prompt_parts.extend(
            [
                "",
                "**参数含义说明**：",
                f"- 本次查询针对 **{self.metric_name}** 指标进行 **{analysis_desc}**",
                f"- 数据时间范围为 **{time_range}**",
            ]
        )

        if self.drilldown_dimension:
            prompt_parts.append(f"- 按 **{self.drilldown_dimension}** 维度进行下钻分析")

        if self.where:
            prompt_parts.append(f"- 应用额外过滤条件：**{self.where}**")

        if self.appid_filter:
            prompt_parts.append(f"- 数据已过滤，仅包含客户AppID为 **{self.appid_filter}** 的数据")

        if self.country_filter:
            prompt_parts.append(f"- 数据已过滤，仅包含 **{self.country_filter}** 地区的数据")
        else:
            prompt_parts.append("- 数据包含全球所有地区")

        if not self.appid_filter:
            prompt_parts.append("- 数据包含所有客户AppID")

        # 为成功率指标添加错误码分布分析提示
        if self.metric_name.endswith("成功率") and not self.metric_name.endswith("错误码分布"):
            prompt_parts.append("- **重要提示**：分析成功率问题时，建议同时关注对应的错误码分布指标")

        prompt_parts.extend(["", "**重要提醒**：分析时请基于以上查询条件来理解数据的覆盖范围和局限性。"])

        return "\n".join(prompt_parts)

    def get_metric(self) -> ZEGOMetric:
        """
        获取指标对象
        """
        metric = metrics_registry.get(self.metric_name)
        if metric is None:
            raise ValueError(f"未找到指标: {self.metric_name}")
        return metric

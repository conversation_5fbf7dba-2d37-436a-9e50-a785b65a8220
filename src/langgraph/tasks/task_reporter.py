import logging

from langgraph.func import task

from src.langgraph.common.utils.context_builder import ContextBuilder
from src.langgraph.common.utils.llm_request_utils import create_token_update, llm_structured_request
from src.langgraph.nodes.common.types import ReporterResult
from src.langgraph.state import State

logger = logging.getLogger(__name__)


# _build_reporter_prompt 函数已废弃，功能已集成到 ContextBuilder 中


@task
async def reporter_task(state: State) -> dict:
    """
    Reporter 任务：请求大模型生成最终结构化汇报，并写入 reporter_result。
    返回：dict 状态增量
    """
    logger.info("🤖reporter task is working.")

    state_update: dict = {"messages": []}

    # 如已有 reporter_result 则直接返回空更新（幂等）
    existing = state.get("reporter_result")
    if existing is not None:
        return {}

    # 使用新的 ContextBuilder 构建完整上下文
    plans = state.get("plans", []) or []
    latest_plan = plans[-1] if plans else None

    ctx = ContextBuilder(state=state)

    # 添加系统角色和规则
    ctx.add_reporter_base()
    ctx.add_ocean_dimension_tips()
    ctx.add_rtc_special_issue()
    ctx.add_json_schema(ReporterResult)

    # 添加上下文信息
    ctx.add_plan_goal(latest_plan).add_plan_thinking(latest_plan).add_relate_dimension_summary()

    # 设置人类任务
    ctx.set_human_task("请根据以上信息生成分析报告。")

    input_messages = ctx.build_messages()
    _, result_parsed, _, input_tokens, output_tokens = await llm_structured_request(
        node_name="reporter",
        input_messages=input_messages,
        schema_type=ReporterResult,
    )

    state_update.update(create_token_update(input_tokens, output_tokens))
    reporter_result: ReporterResult = result_parsed

    # 生成消息并落盘到 state
    msg = reporter_result.to_message(extra_kwargs={"input_tokens": input_tokens, "output_tokens": output_tokens})
    state_update["messages"].append(msg)
    state_update["reporter_result"] = reporter_result

    logger.info(f"[reporter] ✅ reporter_result = \n{reporter_result.model_dump_json(indent=4, exclude_none=False)}\n")

    return state_update

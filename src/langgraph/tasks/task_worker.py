from __future__ import annotations

import logging

from typing import Dict

from langgraph.func import task

from src.langgraph.common.utils.context_builder import ContextBuilder

from src.langgraph.common.utils.llm_request_utils import create_token_update, llm_structured_request
from src.langgraph.nodes.common.data_center import DataCenter
from src.langgraph.nodes.common.types import DAGPlanTask, RetryParams, WorkerParams, WorkerResult

from src.langgraph.state import State

from src.langgraph.zego_tools.ocean_executor import SqlExecutorResult

logger = logging.getLogger(__name__)


class AnalysisWorkerNode:
    """
    维度分析任务：封装重复逻辑，返回 dict 状态增量（Functional 风格）。
    接收 DAGPlanTask 任务，内部统一拉取/使用 WorkerParams
    """

    def __init__(self, task: DAGPlanTask):
        self.task = task

        # 设置日志记录器
        qp = (
            getattr(self.task, "worker_params", None).query_params
            if getattr(self.task, "worker_params", None)
            else None
        )
        qp_key = qp.to_key() if qp else "unknown"
        qp_title = qp.query_title if qp else "unknown"
        self.logger = logging.getLogger(f"{__name__}.{qp_key}_{qp_title}")

    async def execute(self, state: State) -> dict:
        """
        执行维度分析的统一逻辑
        支持查询失败时让大模型调整参数，最多重试一次
        不再维护任务状态（RUNNING/DONE/FAILED），调度以 worker_results 为准
        返回：dict 状态增量
        """
        if not getattr(self.task, "worker_params", None):
            raise ValueError("task.worker_params 为空，无法执行分析")
        query_params = self.task.worker_params.query_params
        task_id = int(getattr(self.task, "id"))
        analysis_name = query_params.query_title or query_params.metric_name
        self.logger.info(f"🤖{analysis_name} task is working.")
        state_update: dict = {"messages": []}

        # 计划对象仅用于上下文，不在此处维护状态（避免 state 计划合并问题）
        plans = state.get("plans", []) or []
        plan = plans[-1] if plans else None

        # 确保有有效的数据
        worker_params = await self._ensure_data_available(analysis_name)

        # 处理查询失败的情况（允许大模型调整参数重试一次）
        if worker_params.has_error():
            worker_params = await self._handle_query_failure(worker_params, state, state_update, analysis_name)

        # 结束前，同步 worker_params 到计划对象（仅便于调试查看，不回写 state）
        if plan and getattr(plan, "tasks", None):
            for t in plan.tasks:
                if int(getattr(t, "id")) == task_id:
                    t.worker_params = worker_params
                    break
            # 不回写 plan；结果通过 worker_results 聚合

        # 无论查询成功还是失败，都要继续分析
        return await self._perform_analysis(worker_params, state_update, state)

    async def _ensure_data_available(self, analysis_name: str) -> WorkerParams:
        """
        确保数据可用，如果WorkerParams中没有数据则进行查询
        """
        wp = getattr(self.task, "worker_params", None)
        if wp and wp.has_data():
            return wp
        elif wp and not wp.has_error():
            # 没有数据也没有错误，尝试从数据中心取
            query_params = wp.query_params

            # 先尝试从DataCenter获取已存储的数据（直接返回 DataFrame 与 error_info）
            df, error_info = await DataCenter.get_dataframe(query_params=query_params)
            if df is not None and not df.empty:
                return WorkerParams(
                    query_params=query_params,
                    sql_result=SqlExecutorResult.success(df, additional_info=error_info),
                )
            else:
                # 数据中心也没有数据，需要查询
                return await DataCenter.query_and_store_data(query_params, f"{analysis_name}_node")
        else:
            # 已有错误，直接返回
            return wp

    async def _handle_query_failure(
        self,
        worker_params: WorkerParams,
        state: State,
        state_update: dict,
        analysis_name: str,
    ) -> WorkerParams:
        """
        处理查询失败，让大模型分析错误并决定是否重试
        """
        err = getattr(getattr(worker_params, "sql_result", None), "error_message", None)
        self.logger.warning(f"查询失败，尝试让大模型调整参数: {err}")

        # 让大模型分析错误并决定是否重试
        retry_params = await self._request_llm_to_adjust_params(worker_params, state)

        input_tokens = retry_params.get("input_tokens", 0)
        output_tokens = retry_params.get("output_tokens", 0)
        state_update.update(create_token_update(input_tokens, output_tokens))

        if not retry_params["parsed"].give_up and retry_params["parsed"].adjusted_query_params:
            # 大模型决定重试，执行新的查询
            self.logger.info(
                f"大模型决定重试，使用调整后的参数执行查询: {retry_params['parsed'].adjusted_query_params.model_dump()}"
            )
            adjusted_worker_params = await DataCenter.query_and_store_data(
                retry_params["parsed"].adjusted_query_params, f"{analysis_name}_node_retry"
            )

            if adjusted_worker_params.has_data():
                # 重试成功
                self.logger.info("重试查询成功")
                return adjusted_worker_params
            else:
                # 重试仍然失败，最终放弃
                self.logger.error("重试查询仍然失败，放弃")
                return adjusted_worker_params
        else:
            # 大模型决定放弃
            self.logger.info(f"大模型决定放弃查询: {retry_params['parsed'].give_up_reason}")
            return worker_params

    async def _request_llm_to_adjust_params(self, worker_params: WorkerParams, state: State) -> dict:
        """
        请求大模型分析错误并调整查询参数
        """
        error_context = worker_params.get_error_context()

        # 使用新的 ContextBuilder 构建完整上下文
        ctx = ContextBuilder(state=state)

        # 添加系统角色和规则
        ctx.add_system_text("你是一个数据查询专家。现在有一个查询失败了，请分析错误原因并决定是否调整参数重试。")
        ctx.add_system_text(error_context)
        ctx.add_system_text(
            "请分析以上错误，并决定：\n"
            "1. 如果错误可以通过调整查询参数解决（如修改维度名称、WHERE条件等），请提供调整后的参数\n"
            "2. 如果错误无法解决（如数据库连接问题、权限问题等），请选择放弃\n\n"
            "常见的可调整错误：\n"
            "- 列名不存在：可能需要修改drilldown_dimension\n"
            "- WHERE条件语法错误：可能需要调整where字段\n"
            "- 时间范围问题：可能需要调整时间参数"
        )
        ctx.add_ocean_dimension_tips()
        ctx.add_rtc_special_issue()
        ctx.add_json_schema(RetryParams)

        # 添加上下文信息
        ctx.add_plan_goal().add_plan_thinking().add_relate_dimension_summary(current_work_id=self.task.id)

        # 设置人类任务
        ctx.set_human_task("请分析以上错误并提供调整建议。")

        input_messages = ctx.build_messages()

        response_raw, response_parsed, response_error, input_tokens, output_tokens = await llm_structured_request(
            node_name=f"{worker_params.query_params.to_key()}_retry",
            input_messages=input_messages,
            schema_type=RetryParams,
        )

        return {
            "raw": response_raw,
            "parsed": response_parsed,
            "error": response_error,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
        }

    async def _perform_analysis(self, worker_params: WorkerParams, state_update: dict, state: State) -> dict:
        """
        执行分析（成功或失败情况都会执行）
        返回：dict 状态增量
        """
        query_params = worker_params.query_params

        # 获取数据提示词（成功或失败都有相应的提示词）
        data_prompt = worker_params.get_data_prompt()

        # 使用新的 ContextBuilder 构建完整上下文
        ctx = ContextBuilder(state=state)

        # 添加系统角色和规则
        ctx.add_base_analysis_role()
        ctx.add_ocean_dimension_tips()
        ctx.add_rtc_special_issue()
        ctx.add_system_text(data_prompt)
        ctx.add_json_schema(WorkerResult)

        # 添加上下文信息
        ctx.add_plan_goal().add_plan_thinking().add_relate_dimension_summary(current_work_id=self.task.id)

        # 设置人类任务
        ctx.set_human_task("请根据以上数据进行分析。")

        input_messages = ctx.build_messages()

        # 请求LLM进行分析
        _, response_parsed, _, input_tokens, output_tokens = await llm_structured_request(
            node_name=query_params.to_key(),
            input_messages=input_messages,
            schema_type=WorkerResult,
        )
        state_update.update(create_token_update(input_tokens, output_tokens))
        worker_result: WorkerResult = response_parsed

        self.logger.info(
            f"[{query_params.to_key()}] ✅ worker_result = \n{worker_result.model_dump_json(indent=4, exclude_none=False)}\n"
        )

        # 如果查询失败，在分析结论中注明
        if worker_params.has_error():
            err = getattr(getattr(worker_params, "sql_result", None), "error_message", None)
            try:
                prefix = f"⚠️ 数据查询失败：{err}\n\n" if err else ""
                worker_result.objective_evidence = prefix + (worker_result.objective_evidence or "")
            except Exception:
                pass

        # 生成消息
        worker_message = worker_result.to_message(
            extra_kwargs={
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "worker_params": worker_params,
            }
        )

        # 更新状态
        state_update["messages"].append(worker_message)

        # 存储分析结果
        state_update["worker_results"] = {query_params.to_key(): worker_result}

        # 存储工作参数结果（包含查询状态）
        state_update["worker_params_results"] = {query_params.to_key(): worker_params}

        return state_update


# _build_prompt_list 方法已废弃，功能已集成到 ContextBuilder 中


@task
async def worker_task(state: State) -> Dict:
    """
    Functional 风格的通用分析任务包装。
    统一返回 dict 状态增量。
    """

    task_obj = state.get("current_task")
    if not task_obj:
        raise ValueError("未找到 current_task，无法执行数据查询分析")

    update = await AnalysisWorkerNode(task=task_obj).execute(state)
    return dict(update or {})

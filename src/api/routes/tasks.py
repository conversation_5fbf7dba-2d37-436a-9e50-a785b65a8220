"""任务相关的API路由。"""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import Any, Dict

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import StreamingResponse
from langchain_core.messages import HumanMessage

from src.api.checkpoint_manager import checkpoint_manager
from src.api.models.task_models import TaskCreateRequest, TaskDetailResponse, TaskListResponse, TaskResponse
from src.langgraph.common.utils.time_utils import get_api_timestamp
from src.langgraph.graph import zego_graph
from src.langgraph.nodes.common.types import z_to_json_str

# ocean_connection 和 themis_connection 已移至健康检查模块

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/tasks", tags=["tasks"])


@router.post("/", response_model=TaskResponse)
async def create_task(request: TaskCreateRequest):
    """创建新任务。"""
    try:
        # 生成唯一的thread_id
        thread_id = f"web_{uuid.uuid4()}"

        # 构建配置
        config = {
            "configurable": {
                "thread_id": thread_id,
                "max_parallel_workers": request.max_parallel_workers,
            },
            "recursion_limit": request.recursion_limit,
        }

        # 启动任务（异步执行，不等待完成）
        asyncio.create_task(_execute_task_async(request.user_input, config))

        # 立即返回任务信息
        response = TaskResponse(
            thread_id=thread_id,
            status="pending",
            created_at=datetime.now(),
            user_input=request.user_input,
            config=config,
        )

        logger.info(f"Created task {thread_id} with input: {request.user_input[:100]}...")
        return response

    except Exception as e:
        logger.error(f"Failed to create task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=TaskListResponse)
async def get_tasks(
    limit: int = Query(default=20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(default=0, ge=0, description="偏移量"),
):
    """获取任务列表。"""
    try:
        tasks = await checkpoint_manager.get_task_list(limit=limit, offset=offset)

        # 转换为响应模型
        task_responses = []
        for task in tasks:
            task_response = TaskResponse(
                thread_id=task["thread_id"],
                status=task["status"],
                created_at=task["created_at"],
                user_input=task["user_input"],
                config=task["config"],
            )
            task_responses.append(task_response)

        response = TaskListResponse(tasks=task_responses, total=len(task_responses))  # 简化实现，实际应该查询总数

        logger.info(f"Retrieved {len(task_responses)} tasks")
        return response

    except Exception as e:
        logger.error(f"Failed to get tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{thread_id}", response_model=TaskDetailResponse)
async def get_task_detail(thread_id: str):
    """获取任务详情。"""
    try:
        task_detail = await checkpoint_manager.get_task_detail(thread_id)

        if not task_detail:
            raise HTTPException(status_code=404, detail=f"Task {thread_id} not found")

        response = TaskDetailResponse(
            thread_id=task_detail["thread_id"],
            status=task_detail["status"],
            created_at=task_detail["created_at"],
            updated_at=task_detail.get("updated_at"),
            user_input=task_detail["user_input"],
            config=task_detail["config"],
            checkpoints=task_detail.get("checkpoints", []),
            messages=task_detail.get("messages", []),
        )

        logger.info(f"Retrieved task detail for {thread_id}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get task detail for {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{thread_id}/stream")
async def stream_task_execution(thread_id: str, request: TaskCreateRequest):
    """流式执行任务，返回Server-Sent Events。"""
    try:
        # 构建配置
        config = {
            "configurable": {
                "thread_id": thread_id,
                "max_parallel_workers": request.max_parallel_workers,
            },
            "recursion_limit": request.recursion_limit,
        }

        # 生成流式响应
        async def event_generator():
            try:
                logger.info(f"Starting streaming task execution for thread_id: {thread_id}")

                input_state = {"messages": [HumanMessage(content=request.user_input)]}

                async with zego_graph() as graph:
                    # 使用多种stream_mode获取完整信息
                    stream_mode = ["values", "messages", "updates", "custom"]

                    async for ev in graph.astream(input_state, config, stream_mode=stream_mode, debug=False):
                        # 解析事件类型和值
                        if isinstance(ev, tuple) and len(ev) == 2:
                            etype, evalue = ev
                        else:
                            etype, evalue = "updates", ev

                        # 构建事件对象
                        event_obj = {
                            "type": etype,
                            "value": evalue,
                            "thread_id": thread_id,
                            "timestamp": get_api_timestamp(),
                        }

                        # 发送SSE数据，使用安全序列化（严格输出 JSON 字符串）
                        try:
                            yield f"data: {z_to_json_str(event_obj)}\n\n"
                        except Exception as e:
                            logger.error(f"Failed to serialize event object: {e}")
                            # 发送简化的错误事件（同样输出为 JSON 字符串）
                            error_event = {
                                "type": "serialization_error",
                                "value": f"Failed to serialize event: {str(e)}",
                                "thread_id": thread_id,
                                "timestamp": get_api_timestamp(),
                            }
                            yield f"data: {z_to_json_str(error_event)}\n\n"

                # 发送成功完成事件
                success_obj = {
                    "type": "success",
                    "value": None,
                    "thread_id": thread_id,
                    "timestamp": get_api_timestamp(),
                }
                yield f"data: {z_to_json_str(success_obj)}\n\n"

                logger.info(f"Task execution completed for thread_id: {thread_id}")

            except Exception as e:
                logger.error(f"Task execution failed for thread_id {thread_id}: {e}")
                # 发送错误事件
                error_obj = {
                    "type": "error",
                    "value": str(e),
                    "thread_id": thread_id,
                    "timestamp": get_api_timestamp(),
                }
                yield f"data: {z_to_json_str(error_obj)}\n\n"
            finally:
                # 发送关闭事件
                close_obj = {
                    "type": "close",
                    "value": None,
                    "thread_id": thread_id,
                    "timestamp": get_api_timestamp(),
                }
                yield f"data: {z_to_json_str(close_obj)}\n\n"

        # 返回流式响应
        headers = {
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
        return StreamingResponse(event_generator(), media_type="text/event-stream", headers=headers)

    except Exception as e:
        logger.error(f"Failed to start streaming for {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _execute_task_async(user_input: str, config: Dict[str, Any]):
    """异步执行任务（后台执行，不流式输出）。"""
    try:
        logger.info(f"Starting task execution for thread_id: {config['configurable']['thread_id']}")

        input_state = {"messages": [HumanMessage(content=user_input)]}

        async with zego_graph() as graph:
            # 执行图，但不流式输出（后台执行）
            async for _ in graph.astream(input_state, config, stream_mode="updates", debug=False):
                # 这里可以添加进度更新逻辑，比如通过WebSocket推送
                pass

        logger.info(f"Task execution completed for thread_id: {config['configurable']['thread_id']}")

    except Exception as e:
        logger.error(f"Task execution failed for thread_id {config['configurable']['thread_id']}: {e}")
        # 这里可以更新任务状态为失败

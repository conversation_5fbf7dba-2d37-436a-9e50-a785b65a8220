"""健康检查API路由

提供系统各组件的健康检查端点。
"""

import logging
from typing import Any, Dict

from fastapi import APIRouter, HTTPException

from src.api.health import check_all_databases, check_ocean_db, check_system_health, check_themis_db, HealthStatus

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/health", tags=["health"])


@router.get("/")
async def get_system_health() -> Dict[str, Any]:
    """获取系统整体健康状态

    Returns:
        系统健康状态汇总，包括所有组件的检查结果
    """
    try:
        health_result = await check_system_health()
        return health_result
    except Exception as e:
        logger.error(f"系统健康检查异常: {e}")
        return {"status": HealthStatus.ERROR.value, "message": f"系统健康检查异常: {str(e)}", "error": str(e)}


@router.get("/db")
async def get_database_health() -> Dict[str, Any]:
    """获取所有数据库的健康状态

    Returns:
        所有数据库的健康检查结果
    """
    try:
        db_health = await check_all_databases()
        return db_health
    except Exception as e:
        logger.error(f"数据库健康检查异常: {e}")
        return {"status": HealthStatus.ERROR.value, "message": f"数据库健康检查异常: {str(e)}", "error": str(e)}


@router.get("/db/ocean")
async def get_ocean_database_health() -> Dict[str, Any]:
    """测试Ocean数据库连接是否正常

    Returns:
        Ocean数据库连接状态
    """
    try:
        ocean_health = await check_ocean_db()
        return ocean_health
    except Exception as e:
        logger.error(f"Ocean数据库连接测试异常: {e}")
        return {
            "status": HealthStatus.ERROR.value,
            "message": f"Ocean数据库连接测试异常: {str(e)}",
            "database": "ocean",
            "error": str(e),
        }


@router.get("/db/themis")
async def get_themis_database_health() -> Dict[str, Any]:
    """测试Themis数据库连接是否正常

    Returns:
        Themis数据库连接状态
    """
    try:
        themis_health = await check_themis_db()
        return themis_health
    except Exception as e:
        logger.error(f"Themis数据库连接测试异常: {e}")
        return {
            "status": HealthStatus.ERROR.value,
            "message": f"Themis数据库连接测试异常: {str(e)}",
            "database": "themis",
            "error": str(e),
        }

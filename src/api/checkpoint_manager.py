"""Checkpoint管理器。

从langgraph获取checkpointer，并提供任务历史查询功能。
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from langgraph.checkpoint.base import (
    ChannelVersions,
    Checkpoint,
    CheckpointMetadata,
    CheckpointTuple,
    get_checkpoint_id,
    get_checkpoint_metadata,
    WRITES_IDX_MAP,
)

from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver


logger = logging.getLogger(__name__)


class CheckpointManager:
    """Checkpoint管理器，负责任务历史的查询和管理。"""

    def __init__(self):
        self._checkpointer: Optional[AsyncPostgresSaver] = None
        self._store = None

    async def initialize(self):
        """初始化checkpointer。"""
        try:
            from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
            from langgraph.store.postgres.aio import AsyncPostgresStore

            from src.langgraph.common.models.new_llms import get_default_embedding_model

            # 这里不能使用async with，因为我们需要保持连接
            # 改为直接创建连接实例
            from src.langgraph.memory.store_utils import DB_URI

            # 创建store和checkpointer实例
            store_context = AsyncPostgresStore.from_conn_string(
                DB_URI,
                index={
                    "dims": 1024,
                    "embed": get_default_embedding_model(),
                },
            )
            checkpointer_context = AsyncPostgresSaver.from_conn_string(DB_URI)

            # 进入上下文管理器
            self._store = await store_context.__aenter__()
            self._checkpointer = await checkpointer_context.__aenter__()

            # 初始化
            await self._store.setup()
            await self._checkpointer.setup()

            logger.info("CheckpointManager initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize CheckpointManager: {e}")
            raise

    async def get_task_list(self, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """获取任务列表。

        Args:
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            任务列表
        """
        try:
            # 每次都重新创建连接，避免连接关闭问题
            from src.langgraph.memory.store_utils import get_store_and_checkpointer

            async with get_store_and_checkpointer() as (store, checkpointer):
                # 使用checkpointer.alist({})获取历史
                checkpoints = []
                async for checkpoint in checkpointer.alist({}):
                    checkpoints.append(checkpoint)
                    if len(checkpoints) >= limit + offset:
                        break

                # 应用分页
                paginated_checkpoints = checkpoints[offset : offset + limit]

                tasks = []
                for checkpoint in paginated_checkpoints:
                    task_info = self._format_checkpoint_to_task(checkpoint)
                    if task_info:
                        tasks.append(task_info)

                logger.info(f"Retrieved {len(tasks)} tasks (limit={limit}, offset={offset})")
                return tasks

        except Exception as e:
            logger.error(f"Failed to get task list: {e}")
            raise

    async def get_task_detail(self, thread_id: str) -> Optional[Dict[str, Any]]:
        """获取任务详情。

        Args:
            thread_id: 任务线程ID

        Returns:
            任务详情，如果不存在返回None
        """
        try:
            # 每次都重新创建连接，避免连接关闭问题
            from src.langgraph.memory.store_utils import get_store_and_checkpointer

            async with get_store_and_checkpointer() as (store, checkpointer):
                # 获取指定thread_id的所有checkpoints
                checkpoint_tuples: list[CheckpointTuple] = []
                config_filter = {"configurable": {"thread_id": thread_id}}

                async for checkpoint_tuple in checkpointer.alist(config_filter):
                    checkpoint_tuple: CheckpointTuple = checkpoint_tuple
                    config, checkpoint, metadata, parent_config, pending_writes = checkpoint_tuple
                    checkpoint_tuples.append(checkpoint_tuple)

                if not checkpoint_tuples:
                    return None

                # 按时间排序，最新的在前
                checkpoint_tuples.sort(key=lambda x: x.checkpoint["ts"], reverse=True)
                latest_checkpoint = checkpoint_tuples[0]

                # 格式化任务详情
                task_detail = self._format_checkpoint_to_task_detail(latest_checkpoint, checkpoint_tuples)

                logger.info(f"Retrieved task detail for thread_id: {thread_id}")
                return task_detail

        except Exception as e:
            logger.error(f"Failed to get task detail for {thread_id}: {e}")
            raise

    def _format_checkpoint_to_task(self, checkpoint: CheckpointTuple) -> Optional[Dict[str, Any]]:
        """将checkpoint格式化为任务信息。"""
        try:
            config = checkpoint.config
            thread_id = config.get("configurable").get("thread_id")

            # 从checkpoint中提取状态信息
            messages = self._safe_get_from_checkpoint(checkpoint, "checkpoint.channel_values.messages", [])

            # 提取用户输入（第一条HumanMessage）
            user_input = ""
            if messages:
                for msg in messages:
                    if isinstance(msg, dict) and msg.get("type") == "human":
                        user_input = msg.get("content", "")
                        break
                    elif hasattr(msg, "content") and hasattr(msg, "__class__"):
                        if "Human" in msg.__class__.__name__:
                            user_input = msg.content
                            break

            # 确定任务状态
            status = self._determine_task_status(checkpoint)

            # 获取时间戳
            ts = self._safe_get_from_checkpoint(checkpoint, "metadata.ts") or self._safe_get_from_checkpoint(
                checkpoint, "ts"
            )

            return {
                "thread_id": thread_id,
                "status": status,
                "created_at": self._parse_timestamp(ts),
                "user_input": user_input,
                "config": config,
            }

        except Exception as e:
            logger.warning(f"Failed to format checkpoint: {e}")
            return None

    def _format_checkpoint_to_task_detail(
        self, latest_checkpoint: CheckpointTuple, all_checkpoints: list[CheckpointTuple]
    ) -> Dict[str, Any]:
        """将checkpoint格式化为详细任务信息。"""
        task_info = self._format_checkpoint_to_task(latest_checkpoint)
        if not task_info:
            raise ValueError("Failed to format checkpoint to task")

        # 添加详细信息
        task_info.update(
            {
                "updated_at": self._parse_timestamp(getattr(latest_checkpoint, "ts", None)),
                "checkpoints": [self._format_checkpoint_summary(cp) for cp in all_checkpoints],
                "messages": self._extract_messages_from_checkpoints(all_checkpoints),
            }
        )

        return task_info

    def _format_checkpoint_summary(self, checkpoint) -> Dict[str, Any]:
        """格式化checkpoint摘要。"""
        return {
            "ts": self._safe_get_from_checkpoint(checkpoint, "ts"),
            "step": self._safe_get_from_checkpoint(checkpoint, "metadata.step", -1),
            "writes": self._safe_get_from_checkpoint(checkpoint, "checkpoint.writes", {}),
            "metadata": self._safe_get_from_checkpoint(checkpoint, "metadata", {}),
        }

    def _extract_messages_from_checkpoints(self, checkpoints: List) -> List[Dict[str, Any]]:
        """从checkpoints中提取消息历史。"""
        messages = []
        for checkpoint in reversed(checkpoints):  # 按时间正序
            # 使用安全的属性访问方法
            checkpoint_messages = self._safe_get_from_checkpoint(checkpoint, "checkpoint.channel_values.messages", [])

            for msg in checkpoint_messages:
                if isinstance(msg, dict):
                    messages.append(msg)
                elif hasattr(msg, "dict"):
                    messages.append(msg.dict())
                else:
                    # 尝试提取基本信息
                    messages.append(
                        {
                            "type": getattr(msg, "type", "unknown"),
                            "content": getattr(msg, "content", str(msg)),
                            "name": getattr(msg, "name", None),
                        }
                    )

        return messages

    def _determine_task_status(self, checkpoint) -> str:
        """根据checkpoint确定任务状态。"""
        # 简单的状态判断逻辑，可以根据实际需要扩展
        try:
            step = self._safe_get_from_checkpoint(checkpoint, "metadata.step", -1)
            writes = self._safe_get_from_checkpoint(checkpoint, "checkpoint.writes", {})

            if step == -1:
                return "pending"
            elif writes:
                return "running"
            else:
                return "completed"
        except Exception:
            return "unknown"

    def _parse_timestamp(self, ts: Any) -> datetime:
        """解析时间戳。"""
        if isinstance(ts, datetime):
            return ts
        elif isinstance(ts, str):
            try:
                return datetime.fromisoformat(ts.replace("Z", "+00:00"))
            except:
                pass

        # 如果解析失败，返回当前时间
        return datetime.now()


# 全局实例
checkpoint_manager = CheckpointManager()
